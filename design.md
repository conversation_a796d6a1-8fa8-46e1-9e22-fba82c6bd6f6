# MindfulJourney Design Specification

## Information Architecture (IA)

### Site Structure
```
MindfulJourney/
├── Landing Page (/)
├── Authentication (/auth)
│   ├── Sign In (/auth/signin)
│   ├── Sign Up (/auth/signup)
│   └── Reset Password (/auth/reset)
├── Dashboard (/dashboard)
│   ├── Overview
│   ├── Recent Entries
│   ├── Goals Progress
│   └── AI Insights
├── Journal (/journal)
│   ├── New Entry (/journal/new)
│   ├── Entry List (/journal/entries)
│   └── Entry Detail (/journal/[id])
├── AI Agents (/agents)
│   ├── Mindfulness Coach
│   ├── Reflection Guide
│   └── Goal Tracker
├── Goals (/goals)
│   ├── Set Goals (/goals/new)
│   ├── Track Progress (/goals/track)
│   └── Goal Analytics (/goals/analytics)
├── Insights (/insights)
│   ├── Personal Analytics
│   ├── Mood Patterns
│   └── Growth Metrics
├── Settings (/settings)
│   ├── Profile
│   ├── Preferences
│   └── Privacy
├── Billing (/billing)
│   ├── Subscription
│   ├── Payment Methods
│   └── Usage
├── Support (/support)
│   ├── Help Center
│   ├── Contact
│   └── FAQ
└── Documentation (/docs)
    ├── Getting Started
    ├── Features Guide
    └── API Reference
```

*Reference: [Next.js App Router structure](researchstack.md#nextjs-framework) for file-based routing*

## Page Layouts & User Flows

### 1. Landing Page Layout
```
Header: Logo + Navigation + CTA
Hero: Value Prop + Demo + Sign Up
Features: AI-Powered + Privacy + Analytics
Pricing: Tiers + Comparison
Testimonials: User Stories
Footer: Links + Legal
```

### 2. Dashboard Layout (Post-Auth)
```
Sidebar Navigation:
- Dashboard (active)
- Journal
- AI Agents  
- Goals
- Insights
- Settings

Main Content Area:
- Welcome Message
- Quick Actions (New Entry, Set Goal)
- Recent Activity Feed
- AI Insights Widget
- Progress Charts
```

### 3. Journal Entry Flow
```
1. Entry Creation (/journal/new)
   - Rich text editor
   - Mood selector
   - Tags input
   - Privacy settings

2. AI Enhancement
   - Gemini analysis for insights
   - Suggested reflections
   - Mood pattern detection

3. Save & Review
   - Auto-save drafts
   - Preview mode
   - Share options
```

*Reference: [Supabase Realtime](researchstack.md#supabase) for live collaboration features*

## Database Schema

### Core Tables

#### 1. Users Table
```sql
users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  subscription_tier TEXT DEFAULT 'free',
  stripe_customer_id TEXT,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2. Entries Table
```sql
entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title TEXT,
  content TEXT NOT NULL,
  mood_score INTEGER CHECK (mood_score >= 1 AND mood_score <= 10),
  tags TEXT[],
  is_private BOOLEAN DEFAULT true,
  ai_insights JSONB,
  word_count INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 3. Goals Table
```sql
goals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  category TEXT, -- 'mindfulness', 'health', 'personal', 'career'
  target_value NUMERIC,
  current_value NUMERIC DEFAULT 0,
  unit TEXT, -- 'days', 'times', 'minutes'
  target_date DATE,
  status TEXT DEFAULT 'active', -- 'active', 'completed', 'paused'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 4. Insights Table
```sql
insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  type TEXT NOT NULL, -- 'mood_pattern', 'goal_progress', 'writing_style'
  title TEXT NOT NULL,
  description TEXT,
  data JSONB, -- AI-generated insights data
  confidence_score NUMERIC CHECK (confidence_score >= 0 AND confidence_score <= 1),
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Row Level Security (RLS) Policies
```sql
-- Users can only access their own data
ALTER TABLE entries ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own entries" ON entries FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own entries" ON entries FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own entries" ON entries FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own entries" ON entries FOR DELETE USING (auth.uid() = user_id);
```

*Reference: [Supabase RLS](researchstack.md#supabase) for data security implementation*

## API Routes Structure

### Authentication Routes
```
POST /api/auth/signup
POST /api/auth/signin  
POST /api/auth/signout
POST /api/auth/reset-password
```

### Journal Routes
```
GET    /api/entries          # List user entries
POST   /api/entries          # Create new entry
GET    /api/entries/[id]     # Get specific entry
PUT    /api/entries/[id]     # Update entry
DELETE /api/entries/[id]     # Delete entry
POST   /api/entries/[id]/ai  # Generate AI insights
```

### Goals Routes
```
GET    /api/goals            # List user goals
POST   /api/goals            # Create new goal
PUT    /api/goals/[id]       # Update goal progress
DELETE /api/goals/[id]       # Delete goal
GET    /api/goals/analytics  # Goal analytics data
```

### AI Integration Routes
```
POST /api/ai/analyze-entry   # Gemini analysis of journal entry
POST /api/ai/generate-prompt # AI-generated reflection prompts
POST /api/ai/mood-insights   # Mood pattern analysis
POST /api/ai/goal-suggestions # AI goal recommendations
```

### Stripe Integration Routes
```
POST /api/stripe/create-checkout    # Create Stripe checkout session
POST /api/stripe/webhook           # Handle Stripe webhooks
GET  /api/stripe/customer          # Get customer info
POST /api/stripe/cancel-subscription # Cancel subscription
```

*Reference: [Next.js API Routes](researchstack.md#nextjs-framework) and [Stripe API](researchstack.md#stripe)*

## Stripe Pricing Tiers

### Free Tier - "Mindful Start"
- **Price**: $0/month
- **Features**:
  - 10 journal entries per month
  - Basic mood tracking
  - 1 personal goal
  - Limited AI insights (5 per month)
  - Community support

### Pro Tier - "Mindful Growth" 
- **Price**: $9.99/month or $99/year (17% savings)
- **Features**:
  - Unlimited journal entries
  - Advanced mood analytics
  - Unlimited goals with progress tracking
  - Full AI insights and coaching
  - Export data (PDF, JSON)
  - Priority email support
  - Dark mode themes

### Premium Tier - "Mindful Mastery"
- **Price**: $19.99/month or $199/year (17% savings)
- **Features**:
  - Everything in Pro
  - Advanced AI coaching sessions
  - Custom goal templates
  - Team/family sharing (up to 5 members)
  - API access for integrations
  - White-label options
  - 1-on-1 onboarding call
  - Phone support

*Reference: [Stripe Billing](researchstack.md#stripe) for subscription management*

## Dark Mode Color Tokens

### Primary Colors
```css
:root {
  /* Light mode */
  --color-primary: #6366f1;      /* Indigo-500 */
  --color-primary-dark: #4f46e5; /* Indigo-600 */
  --color-primary-light: #818cf8; /* Indigo-400 */
}

[data-theme="dark"] {
  /* Dark mode */
  --color-primary: #818cf8;      /* Indigo-400 */
  --color-primary-dark: #6366f1; /* Indigo-500 */
  --color-primary-light: #a5b4fc; /* Indigo-300 */
}
```

### Background Colors
```css
:root {
  --bg-primary: #ffffff;         /* White */
  --bg-secondary: #f8fafc;       /* Slate-50 */
  --bg-tertiary: #f1f5f9;        /* Slate-100 */
}

[data-theme="dark"] {
  --bg-primary: #0f172a;         /* Slate-900 */
  --bg-secondary: #1e293b;       /* Slate-800 */
  --bg-tertiary: #334155;        /* Slate-700 */
}
```

### Text Colors
```css
:root {
  --text-primary: #0f172a;       /* Slate-900 */
  --text-secondary: #475569;     /* Slate-600 */
  --text-muted: #94a3b8;         /* Slate-400 */
}

[data-theme="dark"] {
  --text-primary: #f8fafc;       /* Slate-50 */
  --text-secondary: #cbd5e1;     /* Slate-300 */
  --text-muted: #64748b;         /* Slate-500 */
}
```

*Reference: [Tailwind CSS dark mode](researchstack.md#tailwind-css) utilities*

## Typography System

### Font Stack
```css
/* Primary font - Inter for UI */
--font-sans: 'Inter', ui-sans-serif, system-ui, sans-serif;

/* Secondary font - Crimson Text for journal content */
--font-serif: 'Crimson Text', ui-serif, Georgia, serif;

/* Monospace for code */
--font-mono: 'JetBrains Mono', ui-monospace, monospace;
```

### Type Scale
```css
--text-xs: 0.75rem;     /* 12px */
--text-sm: 0.875rem;    /* 14px */
--text-base: 1rem;      /* 16px */
--text-lg: 1.125rem;    /* 18px */
--text-xl: 1.25rem;     /* 20px */
--text-2xl: 1.5rem;     /* 24px */
--text-3xl: 1.875rem;   /* 30px */
--text-4xl: 2.25rem;    /* 36px */
```

### Font Weights
```css
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

*Reference: [Next.js Font optimization](researchstack.md#nextjs-framework) for performance*

---

## Design Decisions Rationale

### 1. Dark Mode First Approach
- **Reasoning**: Journaling often happens in evening/night hours
- **Implementation**: CSS custom properties with Tailwind's dark mode utilities
- **Reference**: [Tailwind dark mode documentation](researchstack.md#tailwind-css)

### 2. AI-Powered Insights Architecture  
- **Reasoning**: Leverage Gemini's multimodal capabilities for deep journal analysis
- **Implementation**: Async processing with Supabase functions
- **Reference**: [Google Gemini capabilities](researchstack.md#google-gemini-ai)

### 3. Privacy-First Database Design
- **Reasoning**: Mental health data requires maximum security
- **Implementation**: Supabase RLS policies + encryption at rest
- **Reference**: [Supabase security features](researchstack.md#supabase)

### 4. Subscription Model Choice
- **Reasoning**: Freemium model to lower barrier to entry, premium features for power users
- **Implementation**: Stripe subscription management with usage-based limits
- **Reference**: [Stripe billing products](researchstack.md#stripe)

---

*Design specification completed for MindfulJourney SaaS platform*
